
# MindBack Development Plan

## Overview
This development plan implements the **Parallel Three-Stage Prompt Architecture** as outlined in `20250611_Unified_Prompt_Strategy.md`.

### **Key Strategy Updates:**
- **Parallel Development**: New three-stage pipeline developed alongside existing system
- **No Backend Modifications**: Existing `llm.py` and current prompting remain untouched
- **MBCP-Native Memory**: All memory uses MBCP-compatible formats, no Letta integration
- **Base64 for Large Data**: `[ZIPPED]::` blocks for structured documents, readable blocks for semantic context
- **Memory-First Implementation**: Build memory foundation before three-stage pipeline

### **Architecture Components:**
1. **Routing Stage**: Decision-making and intent classification (`routing_prompt.yaml`)
2. **Memory Stage**: MBCP-compatible context retrieval and backstory enrichment
3. **Execution Stage**: Unified response generation with full context (`unified_Prompt.yaml`)

---

## 🚨 Critical Issues: Store Architecture Migration

### **Phase 0: React Hooks Architecture Fix - COMPLETED ✅**
The React Hooks violations have been resolved through comprehensive store architecture refactoring:

#### **Completed Fixes**
- [x] **0.1** Audit all store-related functions to identify internal hook usage
- [x] **0.2** Create pure store access layer without React hooks
- [x] **0.3** Implement store context pattern for proper hook usage
- [x] **0.4** Refactor MindSheetTabs.tsx to use contexts
- [x] **0.5** Refactor MindSheetWrapper.tsx to use contexts
- [x] **0.6** Refactor MindMapCanvasWrapper.tsx to use contexts
- [x] **0.7** Remove all direct store function calls from components
- [x] **0.8** Validate consistent hook order across all renders
- [x] **0.9** Test application functionality without React errors

**Status**: ✅ COMPLETED - Application loads without React Hooks violations

### **Phase 0.1: Store Architecture Migration - IN PROGRESS**

#### **Root Cause Analysis**
The critical system issues stem from **incomplete store architecture migration**:
1. **Persistence Layer**: Still uses old direct store access patterns (`useChatStore.getState()`)
2. **UI Layer**: Uses new context-based store access patterns (`useChatStoreContext()`)
3. **Result**: Two separate store instances - data saved to one, UI reads from another

#### **Store Fragmentation Issues**
- **Auto-Save System**: Persistence service saves to different store instance than UI reads from
- **Project Save**: Current project state not tracked due to store fragmentation
- **MindMap Canvas**: Store registry integration incomplete - canvas cannot access sheet stores
- **Chat Memory**: Conversation history lost due to persistence/UI store mismatch

#### **Phase 0.1 Implementation Tasks**
- [ ] **0.1.1** Fix MindBookPersistenceService store access patterns
  - [ ] Replace all `useChatStore.getState()` with context-based access
  - [ ] Replace all `useMindBookStore.getState()` with context-based access
  - [ ] Replace all `useContextStore.getState()` with context-based access
  - [ ] Update all persistence methods to use provided store instances

- [ ] **0.1.2** Complete Store Registry Integration
  - [ ] Ensure canvas components can access sheet stores through registry
  - [ ] Fix MindMap initialization and rendering issues
  - [ ] Validate store registry context propagation

- [ ] **0.1.3** Restore Core Functionality
  - [ ] Fix auto-save system for project switching
  - [ ] Restore project update/save functionality (not just "Save As")
  - [ ] Restore chat history persistence and restoration
  - [ ] Validate complete session restoration workflow

- [ ] **0.1.4** Integration Testing
  - [ ] Test all core functionalities end-to-end
  - [ ] Validate store consistency across persistence and UI layers
  - [ ] Ensure no regression in React Hooks compliance

**Priority**: CRITICAL - Must be completed before Phase 1.1 implementation

---

## 🧠 Phase 1: MBCP-Native Memory Foundation

### **1.1 Memory System Implementation**
**Objective**: Build MBCP-compatible memory system integrated with fixed store architecture

#### **Prerequisites**: Phase 0.1 (Store Architecture Migration) must be completed first

#### **Memory Services Integration**
- [x] **1.1.1** `MBCPMemoryService` exists (`backend/api/services/mbcp_memory_service.py`)
  - [x] Conversation threading for ChatFork lineage implemented
  - [x] Parent context retrieval for forked chats implemented
  - [x] MBCP-compatible memory storage format implemented
  - [ ] **Integration Required**: Connect to frontend persistence layer

- [ ] **1.1.2** Create `ContextBlockGenerator` (new file: `backend/api/services/context_block_generator.py`)
  - [ ] Generate `[CTX]::` compressed contextual metadata
  - [ ] Generate `[MEM]::` structured conversation history
  - [ ] Generate `[ZIPPED]::` Base64 encoded large data blocks
  - [ ] Create context compression utilities

- [x] **1.1.3** `MemorySnapshotService` exists with MBCP support (`frontend/src/core/services/MemorySnapshotService.ts`)
  - [x] MBCP-compatible snapshot format implemented
  - [x] Snapshot collection functionality implemented
  - [ ] **Integration Required**: Connect with MBCPMemoryService for memory retrieval
  - [ ] **Enhancement Required**: Add memory relevance scoring for context blocks

#### **Frontend Memory Integration**
- [x] **1.1.4** `ChatMemoryService` exists (`frontend/src/services/ChatMemoryService.ts`)
  - [x] Conversation threading capabilities implemented
  - [x] Parent context capture for forks implemented
  - [ ] **Integration Required**: Connect with backend MBCPMemoryService
  - [ ] **Enhancement Required**: Implement memory block preparation for API calls

- [ ] **1.1.5** Create memory testing interface
  - [ ] Test memory retrieval with existing ChatFork workflow
  - [ ] Validate MBCP format consistency
  - [ ] Test Base64 encoding/decoding for large contexts

#### **Event Sourcing Foundation**
- [ ] **1.1.6** Implement Event-Based Memory System
  - [ ] Replace "double memory" architecture with event sourcing
  - [ ] Create lightweight event logging in MBCP format
  - [ ] Implement event-based state reconstruction
  - [ ] Add event stream management for memory queries

- [ ] **1.1.7** Smart Memory Triggers
  - [ ] Implement content-based snapshot triggers (LLM responses, sheet creation)
  - [ ] Add debouncing for excessive snapshots
  - [ ] Create memory relevance scoring and retention policies

## 🚀 Phase 2: Parallel Three-Stage Pipeline (New Development)

### **2.1 New Pipeline Infrastructure**
**Objective**: Create completely separate three-stage pipeline alongside existing system

#### **New Backend Endpoints (No Existing Code Changes)**
- [ ] **2.1.1** Create new API routes (new file: `backend/api/routes/three_stage_llm.py`)
  - [ ] `/api/llm/three-stage/route` - Stage 1: Routing
  - [ ] `/api/llm/three-stage/memory` - Stage 2: Memory retrieval
  - [ ] `/api/llm/three-stage/execute` - Stage 3: Unified execution
  - [ ] `/api/llm/three-stage/pipeline` - Full pipeline endpoint

- [ ] **2.1.2** Create `ThreeStageOrchestrator` (new file: `backend/api/services/three_stage_orchestrator.py`)
  - [ ] Implement stage progression logic
  - [ ] Add conditional memory retrieval
  - [ ] Create pipeline state management
  - [ ] Add comprehensive logging and error handling

- [ ] **2.1.3** Create new prompt service (new file: `backend/api/services/three_stage_prompt_service.py`)
  - [ ] Load three-stage specific prompts
  - [ ] Handle context block injection
  - [ ] Manage Base64 encoding for large data

#### **Three-Stage Prompt Templates**
- [ ] **2.1.4** Finalize routing prompt (`routing_prompt.yaml`)
  - [ ] Test routing decision accuracy across intent types
  - [ ] Optimize for token efficiency with gpt-3.5-turbo
  - [ ] Add comprehensive routing examples and edge cases

- [ ] **2.1.5** Complete unified execution prompt (`unified_Prompt.yaml`)
  - [ ] Validate MBCP format consistency across all sheet types
  - [ ] Test context block handling (`[CTX]::`, `[MEM]::`, `[ZIPPED]::`)
  - [ ] Optimize for different response types (mindmap, chatfork, agent tasks)

### **2.2 Frontend Pipeline Integration**
**Objective**: Add three-stage pipeline option to frontend without breaking existing functionality

#### **Pipeline Selection Interface**
- [ ] **2.2.1** Create pipeline selection mechanism
  - [ ] Add toggle between legacy and three-stage pipeline
  - [ ] Implement feature flag for gradual rollout
  - [ ] Create A/B testing framework

- [ ] **2.2.2** Update governance box for three-stage support
  - [ ] Add new API endpoints to LLM service calls
  - [ ] Implement pipeline stage indicators in UI
  - [ ] Add three-stage specific error handling

#### **Memory-Enhanced ChatFork**
- [ ] **2.2.3** Enhance ChatFork creation with memory context
  - [ ] Implement parent context capture using new memory services
  - [ ] Add backstory context to three-stage fork requests
  - [ ] Update fork UI to show context lineage and memory blocks

- [ ] **2.2.4** Create memory visualization components
  - [ ] Add conversation thread display for debugging
  - [ ] Implement context block viewer (`[CTX]::`, `[MEM]::`, `[ZIPPED]::`)
  - [ ] Create memory injection indicators in UI

## 🔧 Phase 3: Advanced Features and Optimization

### **3.1 Base64 Large Data Handling**
**Objective**: Implement efficient handling of large structured data

#### **ZIPPED Block Implementation**
- [ ] **3.1.1** Create Base64 encoding service
  - [ ] Implement compression for large mindbook states
  - [ ] Add encoding for financial statements and documents
  - [ ] Create agent log compression and encoding

- [ ] **3.1.2** Optimize Base64 strategy
  - [ ] Test token efficiency vs. readable format
  - [ ] Implement smart compression thresholds
  - [ ] Add decode instruction optimization for LLMs

### **3.2 Performance and Migration**
**Objective**: Optimize performance and plan migration strategy

#### **Performance Optimization**
- [ ] **3.2.1** Implement pipeline performance monitoring
  - [ ] Add stage-specific latency tracking
  - [ ] Monitor token usage across stages
  - [ ] Create performance comparison with legacy system

- [ ] **3.2.2** Add caching and optimization
  - [ ] Cache context blocks for repeated queries
  - [ ] Implement memory block caching
  - [ ] Optimize Base64 encoding/decoding

#### **Migration Strategy**
- [ ] **3.2.3** Plan gradual migration
  - [ ] Create feature-by-feature migration plan
  - [ ] Implement rollback mechanisms
  - [ ] Plan legacy system deprecation timeline

---

## � Phase 4: Advanced Memory and Event Sourcing

### **4.1 Event Sourcing Implementation**
**Objective**: Solve double memory issue with event-based approach

#### **Event-Based Memory System**
- [ ] **4.1.1** Create `EventSourcingService` (new file)
  - [ ] Implement lightweight event logging in MBCP format
  - [ ] Add event-based state reconstruction
  - [ ] Create event stream management for memory queries

- [ ] **4.1.2** Enhance snapshot system for continuous memory
  - [ ] Implement event-driven snapshot triggers
  - [ ] Add incremental snapshot updates based on events
  - [ ] Create snapshot-based memory queries with event gaps

#### **Smart Memory Triggers**
- [ ] **4.1.3** Implement intelligent snapshot triggers
  - [ ] Add content-based snapshot triggers (LLM responses, sheet creation)
  - [ ] Implement debouncing for excessive snapshots
  - [ ] Create memory relevance scoring and retention policies

### **4.2 Tool Integration**
**Objective**: Integrate external tools through three-stage pipeline

#### **Firecrawl Integration**
- [ ] **4.2.1** Implement Firecrawl routing in Stage 1
  - [ ] Add web search detection in routing prompt
  - [ ] Create Firecrawl API integration service
  - [ ] Implement tool result formatting for MBCP

- [ ] **4.2.2** Create tool delegation framework
  - [ ] Add generic tool routing mechanism
  - [ ] Implement tool result caching
  - [ ] Create tool-specific context block generation

---

## 📋 Legacy Items (Preserved from Original Plan)

### **Legacy Phase 1: Minimal Change - Copy Existing Pattern**
- [ ] **1.1** Find the existing teleological block in `backend/api/routes/llm.py` (around line 128)
- [ ] **1.2** Copy the exact same logic and add it as an `elif` block for exploratory
- [ ] **1.3** Change only these 2 things:
  - [ ] `'teleological'` → `'exploratory'`
  - [ ] `'initiate_mindmap2'` → `'initiate_chatfork2'`
- [ ] **1.4** Test that teleological still works (regression test)
- [ ] **1.5** Test that exploratory now works

### **UI Cleanup Tasks**
- [ ] Remove ChatFork header with non-functioning buttons
- [ ] Fix Windows taskbar implementation for start/stop app

### **Settings Enhancement (2025.06.08)**
- [ ] Expand context settings with:
  - [ ] Tech stack configuration (LLM APIs, Firecrawl, etc.)
  - [ ] Knowledge source integration (EU, Destatis, Polymarket)
  - [ ] Data source hooks for changing data

---

## 🔧 Technical Implementation Details

### **Three-Stage Pipeline Architecture**

#### **Stage 1: Routing (`routing_prompt.yaml`)**
```
Input: [CTX]:: + [USER]::
Output: { route, requires_memory, tool }
Models: gpt-3.5-turbo (fast, cheap)
```

**Routing Decisions:**
- `factual_response`: Direct answer, no further processing
- `embedding_and_continue`: Route to Stage 3 with memory
- `firecrawl`: External tool delegation

#### **Stage 2: Memory Retrieval (Conditional)**
```
Triggers: requires_memory = true
Sources: ChatMemoryService, Snapshots, ContextStore
Output: [MEM]:: block for Stage 3
```

**Memory Strategies:**
- **Conversation Threading**: Parent context for forks
- **Snapshot Chaining**: Historical state evolution
- **Context Injection**: Foundational/Strategic/Operational settings
- **Event Sourcing**: Lightweight action logging

#### **Stage 3: Unified Execution (`unified_Prompt.yaml`)**
```
Input: [CTX]:: + [MEM]:: + [USER]::
Output: MBCP-formatted response
Models: gpt-4o (complex reasoning)
```

**Response Types:**
- **MindMap Nodes**: Structured node creation/expansion
- **ChatFork Threads**: Conversation continuation with backstory
- **Agent Tasks**: Delegation to specialized agents

### **Context Block Specifications**

#### **[CTX]:: Block Format**
```
mm/teleo/topic=Buy&Build/context=Segmentation/sheet=mindmap_001/node=active_node_id
```

**Components:**
- `mm/teleo`: Sheet type and intent
- `topic`: Current discussion topic
- `context`: Active context level
- `sheet`: Current sheet identifier
- `node`: Active node reference

#### **[MEM]:: Block Format**
```
THREAD: parent_conversation_summary
CONTEXT: foundational_context_summary
EVENTS: recent_significant_actions
NODES: related_mindmap_nodes
```

**Memory Sources:**
- **ChatMemoryService**: Recent structured messages
- **ContextStore**: Active context settings
- **RegistrationManager**: Significant user events
- **SnapshotService**: Historical state snapshots

#### **[USER]:: Block Format**
```
Direct user input without modification
```

### **Implementation Priority Matrix**

#### **Critical Priority (Phase 0.1)**
1. **Store Architecture Migration** - Fixes core functionality breakdown
2. **Persistence Layer Consistency** - Eliminates store fragmentation
3. **Auto-Save System Restoration** - Prevents data loss
4. **Session Restoration** - Maintains user workflow continuity

#### **High Priority (Phase 1.1)**
1. **MBCP Memory Integration** - Connects existing services
2. **Event Sourcing Foundation** - Solves double memory architecture
3. **Conversation Threading** - Enables ChatFork context continuity
4. **Memory Block Generation** - Prepares for three-stage pipeline

#### **Medium Priority (Phase 2)**
1. **Three-Stage Pipeline Core** - New prompting architecture
2. **Advanced Memory Features** - Performance optimization
3. **Tool Integration** - Extends capabilities
4. **Context Block Optimization** - Token efficiency

#### **Low Priority (Phase 3)**
1. **Performance Optimization** - Polish and scale
2. **Advanced Debugging** - Developer experience
3. **Monitoring Systems** - Production readiness

### **Success Metrics**

#### **Phase 0.1: Store Architecture Migration**
- [x] **COMPLETED**: Application loads without React Hooks violations
- [x] **COMPLETED**: Project selection from start page works without errors
- [ ] **CRITICAL**: Auto-save works when switching between projects
- [ ] **CRITICAL**: Can save changes to existing projects (not just "Save As")
- [ ] **CRITICAL**: MindMap canvas initializes and renders properly
- [ ] **CRITICAL**: Chat history persists and restores between project switches
- [ ] **CRITICAL**: Complete session restoration workflow functions
- [ ] **CRITICAL**: Store consistency between persistence and UI layers

#### **Phase 1.1: MBCP-Native Memory Foundation**
- [ ] MBCPMemoryService integrated with frontend persistence
- [ ] ChatFork creation includes parent conversation context
- [ ] Memory retrieval works across sheet switches
- [ ] Event sourcing replaces double memory architecture
- [ ] MBCP format consistency maintained across all memory operations

#### **Performance Success**
- [ ] Memory operations add < 500ms overhead to existing workflows
- [ ] Snapshot generation completes within 2s for typical projects
- [ ] Memory retrieval scales with project size efficiently
- [ ] Event sourcing reduces memory storage overhead by 50%

#### **User Experience Success**
- [x] **COMPLETED**: No React errors in browser console
- [x] **COMPLETED**: Consistent hook order across all component renders
- [ ] **CRITICAL**: Seamless project switching with full state restoration
- [ ] **CRITICAL**: No data loss during auto-save operations
- [ ] Conversation continuity maintained across all workflows
- [ ] Context switches preserve working memory
- [ ] Clear feedback on memory and persistence operations

### **Risk Mitigation**

#### **Phase 0.1: Store Architecture Risks**
- [x] **RESOLVED**: React Hooks violations through context-based architecture
- **CRITICAL: Store Fragmentation**: Incomplete migration creates data inconsistency
  - **Mitigation**: Complete all persistence service updates before testing
  - **Validation**: Comprehensive store consistency testing
- **Regression Risk**: Changes to persistence layer could break existing functionality
  - **Mitigation**: Extensive testing of all core workflows
  - **Rollback Plan**: Maintain backup of working persistence service

#### **Phase 1.1: Memory Integration Risks**
- **Memory Overhead**: MBCP memory system could impact performance
  - **Mitigation**: Implement compression and relevance filtering
  - **Monitoring**: Add performance metrics for memory operations
- **Event Sourcing Complexity**: New architecture could introduce bugs
  - **Mitigation**: Gradual rollout with comprehensive testing
  - **Fallback**: Maintain existing snapshot system during transition
- **MBCP Compatibility**: Memory format changes could break existing components
  - **Mitigation**: Extensive testing with existing ChatFork and MindMap workflows
  - **Validation**: Format consistency checks across all memory operations

### **Testing Strategy**

#### **Phase 0.1: Store Architecture Migration Testing**
- [x] **COMPLETED**: React component hook order consistency
- [x] **COMPLETED**: Store access patterns without hook violations
- [x] **COMPLETED**: Project selection from start page without errors
- [x] **COMPLETED**: Component mounting/unmounting without hook violations
- [ ] **CRITICAL**: Auto-save functionality across project switches
- [ ] **CRITICAL**: Project save/update operations
- [ ] **CRITICAL**: MindMap canvas initialization and rendering
- [ ] **CRITICAL**: Chat history persistence and restoration
- [ ] **CRITICAL**: Complete session restoration workflow
- [ ] **CRITICAL**: Store consistency validation between persistence and UI layers

#### **Phase 1.1: MBCP Memory Foundation Testing**
- [ ] Memory service integration testing
- [ ] Conversation threading validation
- [ ] Event sourcing functionality
- [ ] MBCP format consistency validation
- [ ] Memory block generation and retrieval
- [ ] Context block compression and decompression

#### **Integration Testing**
- [ ] End-to-end memory persistence flow
- [ ] ChatFork creation with parent context
- [ ] Memory retrieval across sheet switches
- [ ] Frontend-backend memory service integration
- [ ] Snapshot-based memory reconstruction

#### **Performance Testing**
- [ ] Memory operation latency measurement
- [ ] Snapshot generation performance
- [ ] Event sourcing overhead analysis
- [ ] Memory storage efficiency validation

---

## 📚 Reference Documentation

### **Key Files**
- `20250611_Unified_Prompt_Strategy.md` - Overall strategy and architecture
- `backend/Prompt_library/routing_prompt.yaml` - Stage 1 implementation
- `backend/Prompt_library/unified_Prompt.yaml` - Stage 3 implementation
- `frontend/src/services/ChatMemoryService.ts` - Memory management
- `backend/api/routes/llm.py` - Current LLM routing logic

### **Architecture Alignment**
This implementation directly supports the unified strategy by:
- **Fixing Core Functionality**: Store architecture migration restores essential features
- **Solving Backstory Problem**: MBCP memory integration provides conversation context
- **Addressing Double Memory**: Event sourcing replaces fragmented memory architecture
- **Maintaining MBCP Format**: Consistent structured responses across all memory operations
- **Enabling Future Pipeline**: Solid memory foundation prepares for three-stage architecture
- **Supporting Agent Workflows**: Structured memory handoff mechanisms
