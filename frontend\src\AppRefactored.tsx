/**
 * AppRefactored.tsx
 *
 * Refactored main application component that integrates the MindBook architecture.
 * This version uses the sheet-based approach with MindBook and MindSheets.
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import { ContextPanelPositioned } from './features/context';
import NodeBox from './features/mindmap/components/NodeBox';
import FooterGovernanceButton from './governance/chat/components/FooterGovernanceButton';
import ProjectDialog from './features/mindmap/components/Dialogs/ProjectDialog';
import { useMindMapStore } from './core/state/MindMapStore';
// Import KeyboardManager to ensure it's initialized
import './core/services/KeyboardManager';
import { initKeyboardHandler } from './utils/keyboardHandler';
import { useMindBookPersistence } from './core/hooks/useMindBookPersistence';
import { useMindBookStore } from './core/state/MindBookStore';
import './App.css';
import './styles/mui-overrides.css';
import './styles/animations.css';
import { PositioningManagerProvider } from './core/positioning';

// Import Startup Screen components
import StartupScreen from './components/StartupScreen';
import HamburgerMenu from './components/HamburgerMenu';
import ContextManagerDialog from './features/context/components/ContextManagerDialog';
import ErrorBoundary from './components/ErrorBoundary';


import { ChatForkAdapter } from './components/MindMap/core/adapters/ChatForkAdapter';
import { ChatForkView } from './components/ChatFork';
import RegistrationManager, { EventType } from './core/services/RegistrationManager';
import MemoryTestRunner from './components/testing/MemoryTestRunner';
import { StoreProvider } from './core/context/StoreContexts';
import AppWorkingState from './components/AppWorkingState';

// Inner component that uses the context-based persistence hook
const AppRefactoredInner: React.FC = () => {
  // Get context-based persistence operations
  const { getSessionInfo, restoreAutoSavedSession, clearSession } = useMindBookPersistence();

  // App state management
  const [appState, setAppState] = useState<'startup' | 'working'>('startup');
  const [showHamburgerMenu, setShowHamburgerMenu] = useState(false);
  const [showContextManager, setShowContextManager] = useState(false);

  // State for governance chat
  const [showGovernanceChat, setShowGovernanceChat] = useState(true);
  const [isGovernanceChatCollapsed, setIsGovernanceChatCollapsed] = useState(false);
  const [isGovernanceChatFullyCollapsed, setIsGovernanceChatFullyCollapsed] = useState(false);

  // State for context panel
  const [isContextPanelOpen, setIsContextPanelOpen] = useState(false);

  // State for MindBook Manager - DEPRECATED
  // const [showMindBookManager, setShowMindBookManager] = useState(false);

  // State for session restoration
  const [isRestoringSession, setIsRestoringSession] = useState(false);
  const [userRequestedStartup, setUserRequestedStartup] = useState(false);

  // State for memory test runner
  const [showMemoryTestRunner, setShowMemoryTestRunner] = useState(false);

  // Fix hook order violation: useState must come before any code that uses it
  const [headerLogoRetries, setHeaderLogoRetries] = useState(0);

  // MindBook store
  const mindBookStore = useMindBookStore();

  // Project dialog state from MindMapStore
  const { showProjectDialog, setShowProjectDialog } = useMindMapStore();

  // Initialize global keyboard handler
  useEffect(() => {
    console.log('AppRefactored: Initializing global keyboard handler');
    const cleanup = initKeyboardHandler();
    return cleanup;
  }, []);

  // Initialize the ChatFork global key handler
  useEffect(() => {
    ChatForkAdapter.initializeGlobalKeyHandler();
    console.log('Initialized ChatFork global key handler');
  }, []);

  // REMOVED: Old auto-save logic - now handled by AppWorkingState component using context-based persistence

  // Initialize session restoration on app start
  useEffect(() => {
    const initializeSession = async () => {
      setIsRestoringSession(true);
      
      try {
        // Set up error handling
        window.addEventListener('error', (e) => {
          console.error('Global error:', e.error);
          RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
            message: e.error?.message || e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
          });
        });

        window.addEventListener('unhandledrejection', (e) => {
          console.error('Unhandled promise rejection:', e.reason);
          RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
            message: 'Unhandled Promise Rejection: ' + e.reason,
            type: 'promise_rejection'
          });
        });

        // DISABLED: Automatic session restoration - always show start page first
        console.log('AppRefactored: Automatic session restoration disabled - showing startup screen');

        const sessionInfo = getSessionInfo();
        console.log('AppRefactored: Session info available:', sessionInfo);

        // Always show startup screen first, let user choose to restore session
        console.log('AppRefactored: Showing startup screen - user can choose to restore session');
        setAppState('startup');

      } catch (error) {
        console.error('AppRefactored: Error during session initialization', error);
        RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
          message: 'Session initialization failed: ' + (error instanceof Error ? error.message : String(error)),
          context: 'app_startup'
        });
        setAppState('startup');
      } finally {
        setIsRestoringSession(false);
      }
    };

    initializeSession();
  }, []);

  // REMOVED: Old auto-save logic - now handled by AppWorkingState component using context-based persistence

  // REMOVED: Old page unload auto-save - now handled by AppWorkingState component using context-based persistence

  // Watch for changes in MindBook store to determine app state
  useEffect(() => {
    // TEMPORARILY DISABLED: Auto-switching to working mode to allow seeing start page
    // Only automatically switch to working if user hasn't explicitly requested startup
    // AND we're not currently restoring a session AND app is in startup state
    if (false && // DISABLED: Set to false to prevent auto-switching
        mindBookStore.sheets.length > 0 && 
        appState === 'startup' && 
        !userRequestedStartup && 
        !isRestoringSession) {
      console.log('AppRefactored: Sheets found, automatically switching to working state');
      setAppState('working');
    }
  }, [mindBookStore.sheets.length, appState, userRequestedStartup, isRestoringSession]);

  // Check for auto-saved session on startup and manage app state
  useEffect(() => {
    const checkForAutoSavedSession = async () => {
      try {
        // TEMPORARILY DISABLED: Auto session checking to show start page with saved sessions
        // Only check and auto-switch if user hasn't explicitly requested startup
        if (false && !userRequestedStartup && !isRestoringSession) { // DISABLED
          const sessionInfo = getSessionInfo();
          if (sessionInfo.hasAutoSave || mindBookStore.sheets.length > 0) {
            console.log('AppRefactored: Auto-saved session or existing sheets found, moving to working state');
            setAppState('working');
          }
        }
      } catch (error) {
        console.error('AppRefactored: Failed to check for auto-saved session:', error);
      }
    };

    checkForAutoSavedSession();
  }, [userRequestedStartup, isRestoringSession]);



  // Handle governance close - mark as fully collapsed
  const handleGovernanceClose = () => {
    setShowGovernanceChat(false);
    setIsGovernanceChatFullyCollapsed(true);
  };

  // Handle governance open from footer button
  const handleGovernanceOpen = () => {
    setIsGovernanceChatFullyCollapsed(false);
    setShowGovernanceChat(true);
    setIsGovernanceChatCollapsed(false);
  };

  // Project dialog handlers
  const handleShowProjectDialog = () => {
    setShowProjectDialog(true);
  };

  const handleCloseProjectDialog = () => {
    setShowProjectDialog(false);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Global shortcuts (work in both modes)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'M') {
        event.preventDefault();
        setShowMemoryTestRunner(true);
        console.log('🧪 Opening Memory Test Runner (Ctrl+Shift+M)');
        return;
      }

      // In startup mode, provide hidden shortcuts
      if (appState === 'startup') {
        if (event.key === 'Escape') {
          setShowHamburgerMenu(true);
        } else if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
          event.preventDefault();
          handleNewMindBook();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [appState]);

  // Startup screen handlers
  const handleNavigateToStartup = () => {
    // Set URL parameter to indicate user-requested startup
    const url = new URL(window.location.href);
    url.searchParams.set('userRequested', 'true');
    window.history.replaceState({}, '', url);
    
    setUserRequestedStartup(true);
    setAppState('startup');
    setShowHamburgerMenu(false);
  };

  const handleNavigateToMindBook = () => {
    // DO NOT clear context settings - they should persist with the loaded mindbook
    // Context settings will be automatically restored when a mindbook is loaded
    console.log('AppRefactored: Navigating to MindBook - attempting to restore auto-saved session');

    try {
      // First, try to restore any auto-saved session
      const sessionInfo = getSessionInfo();
      if (sessionInfo.hasAutoSave) {
        console.log('AppRefactored: Found auto-saved session, restoring...');
        const restored = restoreAutoSavedSession();
        if (restored) {
          console.log('AppRefactored: Auto-saved session successfully restored');
        } else {
          console.warn('AppRefactored: Failed to restore auto-saved session');
        }
      } else {
        console.log('AppRefactored: No auto-saved session found');
      }
    } catch (error) {
      console.error('AppRefactored: Error restoring session:', error);
    }

    // Clear URL parameter when going to working state
    const url = new URL(window.location.href);
    url.searchParams.delete('userRequested');
    window.history.replaceState({}, '', url);

    // Close any open dialogs when navigating to working state
    setShowProjectDialog(false);
    setShowHamburgerMenu(false); // Close deprecated hamburger menu

    setUserRequestedStartup(false);
    setAppState('working');
  };

  const handleNewMindBook = () => {
    try {
      console.log('AppRefactored: Creating new MindBook - clearing session first');

      // Use context-based clearSession instead of direct store access
      clearSession();

      // Register new MindBook creation
      RegistrationManager.registerEvent(EventType.MINDBOOK_CREATED, {
        name: 'New MindBook',
        timestamp: new Date().toISOString(),
        method: 'user_initiated'
      });

      // Clear URL parameter when creating new MindBook
      const url = new URL(window.location.href);
      url.searchParams.delete('userRequested');
      window.history.replaceState({}, '', url);

      console.log('AppRefactored: Session, chat, and context cleared, starting fresh MindBook');

      // CRITICAL FIX: Create a default sheet to ensure there's always an active sheet
      setTimeout(() => {
        try {
          // Import the required types and functions
          const { useMindBookStore } = require('./core/state/MindBookStore');
          const { MindSheetContentType } = require('./core/types/MindSheetTypes');
          const { v4: uuidv4 } = require('uuid');

          // Get the MindBookStore directly
          const mindBookStore = useMindBookStore.getState();

          // Create a default mindmap sheet
          const defaultSheetTitle = 'Welcome';
          const mbcpData = {
            mindmap: {
              root: {
                id: uuidv4(),
                text: 'Welcome to MindBack',
                description: 'Start your journey here',
                metadata: {
                  intent: 'teleological',
                  tags: ['welcome']
                },
                children: []
              }
            },
            text: 'Welcome to MindBack',
            description: 'Start your journey here',
            intent: 'teleological'
          };

          const defaultSheetId = mindBookStore.createMindMapSheet(defaultSheetTitle, mbcpData);
          console.log('AppRefactored: Created default sheet:', defaultSheetId);

          // Set it as the active sheet
          mindBookStore.setActiveSheet(defaultSheetId);
          console.log('AppRefactored: Set default sheet as active:', defaultSheetId);

          // Ensure the store registry is also updated
          const { getMindMapStore } = require('./core/state/MindMapStoreFactory');
          getMindMapStore(defaultSheetId);
          console.log('AppRefactored: Initialized store registry for default sheet');

        } catch (error) {
          console.error('AppRefactored: Error creating default sheet:', error);
        }

        // Close deprecated menu and go to working state
        setShowHamburgerMenu(false);
        setUserRequestedStartup(false);
        setAppState('working');
      }, 100);
    } catch (error) {
      console.error('AppRefactored: Error creating new MindBook:', error);
      RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
        message: 'Failed to create new MindBook: ' + (error instanceof Error ? error.message : String(error)),
        context: 'new_mindbook_creation'
      });
    }
  };

  const handleBrowseAll = () => {
    setShowProjectDialog(true);
  };

  const handleLogoClick = () => {
    // Clear any auto-save state first
    const keysToRemove = Object.keys(localStorage).filter(k => k.includes('mindbook_autosave'));
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Clear URL parameters and set userRequested flag
    const url = new URL(window.location.href);
    url.searchParams.delete('userRequested');
    url.searchParams.set('userRequested', 'true');
    window.history.replaceState({}, '', url);
    
    setUserRequestedStartup(true);
    setAppState('startup');
  };

  // Show loading state during session restoration
  if (isRestoringSession) {
    return (
      <div className="app-container" style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        background: '#000000',
        color: 'white'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '24px', marginBottom: '16px' }}>🔄 Restoring your MindBook...</div>
          <div style={{ fontSize: '14px', opacity: 0.8 }}>
            Loading your saved mindsheets and chats
          </div>
        </div>
      </div>
    );
  }

  // Render startup screen when appState is 'startup'
  if (appState === 'startup') {
    return (
      <PositioningManagerProvider>
        <Router>
          <div className="app-container" style={{ background: '#000000', height: '100vh', overflow: 'hidden' }}>
          {/* Full screen startup - no header, no footer */}
          <ErrorBoundary>
            <StartupScreen
              onNavigateToMindBook={handleNavigateToMindBook}
              onNewMindBook={handleNewMindBook}
              onBrowseAll={handleBrowseAll}
            />
          </ErrorBoundary>

            {/* Footer only with mindback.ai */}
            <footer style={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              height: '40px',
              background: 'transparent',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#666666',
              fontSize: '14px',
              fontWeight: '400',
              zIndex: 1000
            }}>
              mindback.ai
            </footer>

            {/* Project Management Dialog - Visible */}
            {showProjectDialog && (
              <ProjectDialog isOpen={showProjectDialog} onClose={handleCloseProjectDialog} />
            )}

            {/* Hidden dialogs - still need these for functionality but invisible */}
            <div style={{ display: 'none' }}>
              {/* Hamburger Menu - hidden but functional for keyboard shortcuts */}
              <ErrorBoundary>
                <HamburgerMenu
                  isOpen={showHamburgerMenu}
                  onClose={() => setShowHamburgerMenu(false)}
                  onOpenContextManager={() => setShowContextManager(true)}
                  onOpenProjectManager={() => setShowProjectDialog(true)}
                  onNavigateToStartup={handleNavigateToStartup}
                />
              </ErrorBoundary>

              {/* Context Manager Dialog */}
              <ContextManagerDialog
                open={showContextManager}
                onClose={() => setShowContextManager(false)}
              />
            </div>

            {/* Memory Test Runner - Available in startup mode too */}
            {showMemoryTestRunner && (
              <MemoryTestRunner
                onClose={() => setShowMemoryTestRunner(false)}
              />
            )}
          </div>
        </Router>
      </PositioningManagerProvider>
    );
  }

  // Render working state (main application interface)
  return (
    <PositioningManagerProvider>
        <Router>
          <AppWorkingState
            showGovernanceChat={showGovernanceChat}
            isGovernanceChatCollapsed={isGovernanceChatCollapsed}
            isGovernanceChatFullyCollapsed={isGovernanceChatFullyCollapsed}
            isContextPanelOpen={isContextPanelOpen}
            headerLogoRetries={headerLogoRetries}
            onGovernanceClose={() => setIsGovernanceChatFullyCollapsed(true)}
            onGovernanceCollapse={() => setIsGovernanceChatCollapsed(!isGovernanceChatCollapsed)}
            onContextPanelToggle={() => setIsContextPanelOpen(!isContextPanelOpen)}
            onLogoClick={handleLogoClick}
            onShowProjectDialog={handleShowProjectDialog}
            onCloseProjectDialog={handleCloseProjectDialog}
            onHeaderLogoRetry={() => setHeaderLogoRetries(headerLogoRetries + 1)}
          />

          {/* Additional components that need to be outside AppWorkingState */}
          {/* Context Panel - Using Positioned Version */}
          <ContextPanelPositioned
            isOpen={isContextPanelOpen}
            onClose={() => setIsContextPanelOpen(false)}
          />

          {/* ChatForkView - Add it here to make it visible */}
          <ChatForkView />

          {/* Hamburger Menu */}
          <ErrorBoundary>
            <HamburgerMenu
              isOpen={showHamburgerMenu}
              onClose={() => setShowHamburgerMenu(false)}
              onOpenContextManager={() => setShowContextManager(true)}
              onOpenProjectManager={() => setShowProjectDialog(true)}
              onNavigateToStartup={handleNavigateToStartup}
            />
          </ErrorBoundary>

          {/* NodeBox Component - Global instance for all MindSheets */}
          <NodeBox />

          {/* Context Manager Dialog */}
          <ContextManagerDialog
            open={showContextManager}
            onClose={() => setShowContextManager(false)}
          />

          {/* Memory Test Runner - Hidden feature (Ctrl+Shift+T) */}
          {showMemoryTestRunner && (
            <MemoryTestRunner
              onClose={() => setShowMemoryTestRunner(false)}
            />
          )}

          {/* Footer Governance Button - only when governance is collapsed */}
          {isGovernanceChatFullyCollapsed && (
            <div style={{ position: 'fixed', bottom: '10px', right: '10px', zIndex: 1000 }}>
              <FooterGovernanceButton
                onClick={handleGovernanceOpen}
              />
            </div>
          )}
        </Router>
      </PositioningManagerProvider>
  );
};

// Main wrapper component that provides store contexts
const AppRefactored: React.FC = () => {
  return (
    <StoreProvider>
      <AppRefactoredInner />
    </StoreProvider>
  );
};

export default AppRefactored;
