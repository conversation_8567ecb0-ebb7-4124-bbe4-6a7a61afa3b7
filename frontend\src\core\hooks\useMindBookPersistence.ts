/**
 * useMindBookPersistence.ts
 * 
 * React hook that provides MindBook persistence functionality using context-based store access.
 * This hook solves the store fragmentation issue by ensuring all persistence operations
 * use the same store instances as the UI components.
 */

import { useCallback } from 'react';
import { useMindBookStoreContext, useChatStoreContext } from '../context/StoreContexts';
import { useContextStore } from '../../features/context/store/ContextStore';
import { pureMindBookPersistenceService } from '../services/MindBookPersistenceService';

/**
 * Hook that provides MindBook persistence operations using context-based store access
 */
export function useMindBookPersistence() {
  // Get store instances from contexts - this ensures we use the same instances as UI components
  const mindBookStore = useMindBookStoreContext();
  const chatStore = useChatStoreContext();
  const contextStore = useContextStore();

  /**
   * Save a MindBook using context-based store access
   */
  const saveMindBook = useCallback((name: string, description?: string): boolean => {
    return pureMindBookPersistenceService.saveMindBookWithStores(
      name,
      description,
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Load a MindBook using context-based store access
   */
  const loadMindBook = useCallback((mindBookId: string): boolean => {
    return pureMindBookPersistenceService.loadMindBookWithStores(
      mindBookId,
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Auto-save session using context-based store access
   */
  const autoSaveSession = useCallback((): boolean => {
    return pureMindBookPersistenceService.autoSaveSessionWithStores(
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Get list of saved MindBooks
   */
  const getMindBooksList = useCallback(() => {
    return pureMindBookPersistenceService.getMindBooksList();
  }, []);

  /**
   * Restore auto-saved session using context-based store access
   */
  const restoreAutoSavedSession = useCallback((): boolean => {
    return pureMindBookPersistenceService.restoreAutoSavedSessionWithStores(
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Clear session using context-based store access
   */
  const clearSession = useCallback((): void => {
    try {
      // Get current session info before clearing for logging
      const currentSheets = mindBookStore.sheets.length;
      const currentName = mindBookStore.name;

      // Register session close event if there was an active session
      if (currentSheets > 0) {
        console.log('Clearing session:', { sheetsCount: currentSheets, sessionName: currentName || 'Unnamed Session' });
      }

      // Clear MindBook store
      mindBookStore.setSheets([]);
      mindBookStore.setActiveSheet(null);

      // Clear chat store
      chatStore.clearMessages();

      // Clear context store
      contextStore.clearCurrentContextSettings();

      // Clear localStorage session data
      const keysToRemove = Object.keys(localStorage).filter(k =>
        k.includes('mindbook') ||
        k.includes('mindmap') ||
        k.includes('session')
      );

      keysToRemove.forEach(key => {
        if (!key.includes('_list') && !key.includes('context_settings')) {
          localStorage.removeItem(key);
        }
      });

      console.log('Session cleared successfully');
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Get session info using context-based store access
   */
  const getSessionInfo = useCallback(() => {
    const autoSave = localStorage.getItem('mindbook_autosave');
    const mindBooksList = pureMindBookPersistenceService.getMindBooksList();

    let lastSaved: number | undefined;
    if (autoSave) {
      try {
        const sessionData = JSON.parse(autoSave);
        lastSaved = sessionData.savedAt;
      } catch (error) {
        console.error('Error parsing auto-save data for timestamp:', error);
      }
    }

    return {
      hasAutoSave: !!autoSave,
      hasSavedMindBooks: mindBooksList.length > 0,
      sheetsCount: mindBookStore.sheets.length,
      lastSaved
    };
  }, [mindBookStore]);

  /**
   * Set context settings using context-based store access
   */
  const setContextSettings = useCallback((contextSettingsId: string): boolean => {
    try {
      const success = contextStore.loadContextSettings(contextSettingsId);

      if (success) {
        console.log('Context settings loaded:', contextSettingsId);
        // Trigger auto-save to associate the context settings with current session
        autoSaveSession();
      }

      return success;
    } catch (error) {
      console.error('Failed to set context settings:', error);
      return false;
    }
  }, [contextStore, autoSaveSession]);

  /**
   * Get current context settings ID using context-based store access
   */
  const getCurrentContextSettingsId = useCallback((): string | null => {
    try {
      return contextStore.currentContextSettings?.id || null;
    } catch (error) {
      console.error('Failed to get current context settings ID:', error);
      return null;
    }
  }, [contextStore]);

  /**
   * Delete a MindBook
   */
  const deleteMindBook = useCallback((mindBookId: string): boolean => {
    return pureMindBookPersistenceService.deleteMindBook(mindBookId);
  }, []);

  return {
    saveMindBook,
    loadMindBook,
    autoSaveSession,
    getMindBooksList,
    restoreAutoSavedSession,
    clearSession,
    getSessionInfo,
    setContextSettings,
    getCurrentContextSettingsId,
    deleteMindBook,
    // Expose store instances for advanced use cases
    stores: {
      mindBookStore,
      chatStore,
      contextStore
    }
  };
}

/**
 * Hook for non-React contexts that need to perform persistence operations
 * This should only be used in special cases where React hooks cannot be used
 */
export function createPersistenceOperations() {
  return {
    /**
     * Save MindBook with explicit store instances
     * Use this only when you have store instances from outside React context
     */
    saveMindBookWithStores: pureMindBookPersistenceService.saveMindBookWithStores.bind(pureMindBookPersistenceService),
    
    /**
     * Load MindBook with explicit store instances
     * Use this only when you have store instances from outside React context
     */
    loadMindBookWithStores: pureMindBookPersistenceService.loadMindBookWithStores.bind(pureMindBookPersistenceService),
    
    /**
     * Auto-save with explicit store instances
     * Use this only when you have store instances from outside React context
     */
    autoSaveSessionWithStores: pureMindBookPersistenceService.autoSaveSessionWithStores.bind(pureMindBookPersistenceService),
    
    /**
     * Get MindBooks list (doesn't require store access)
     */
    getMindBooksList: pureMindBookPersistenceService.getMindBooksList.bind(pureMindBookPersistenceService)
  };
}
